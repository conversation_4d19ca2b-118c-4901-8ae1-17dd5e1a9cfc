"""
Message service for business logic
"""

from typing import List, Optional

from sqlalchemy.orm import Session

from ..models.message import Message as MessageModel, MessageType
from ..schemas.message import MessageCreate


class MessageService:
    """
    Service class for message operations
    """

    def __init__(self, db: Session):
        self.db = db

    def create_message(
        self,
        session_id: str,
        message_type: str,
        content: dict,
        metadata: Optional[dict] = None
    ) -> MessageModel:
        """Create a new message"""
        db_message = MessageModel(
            session_id=session_id,
            type=message_type,
            content=content,
            message_metadata=metadata
        )
        self.db.add(db_message)
        self.db.commit()
        self.db.refresh(db_message)
        return db_message

    def get_messages(self, session_id: str, skip: int = 0, limit: int = 100) -> List[MessageModel]:
        """Get messages for a session"""
        return (
            self.db.query(MessageModel)
            .filter(MessageModel.session_id == session_id)
            .order_by(MessageModel.timestamp)
            .offset(skip)
            .limit(limit)
            .all()
        )

    def get_message(self, message_id: str) -> Optional[MessageModel]:
        """Get a message by ID"""
        return self.db.query(MessageModel).filter(MessageModel.id == message_id).first()
