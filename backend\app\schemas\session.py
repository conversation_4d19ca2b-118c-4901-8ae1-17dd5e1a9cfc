"""
Pydantic schemas for session API
"""

from pydantic import BaseModel, Field
from typing import Optional, Dict, Any
from datetime import datetime
from uuid import UUID
from typing import Union

from ..models.session import SessionStatus


class SessionBase(BaseModel):
    title: Optional[str] = None
    session_metadata: Optional[Dict[str, Any]] = None


class SessionCreate(SessionBase):
    pass


class SessionUpdate(SessionBase):
    status: Optional[SessionStatus] = None


class SessionResponse(SessionBase):
    id: str
    status: SessionStatus
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True
