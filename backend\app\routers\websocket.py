"""
WebSocket endpoints for real-time streaming
"""

import json

from typing import Dict, Any
from fastapi import APIRouter, WebSocket, WebSocketDisconnect, Depends
from sqlalchemy.orm import Session

from ..database import get_db
from ..services.session_service import SessionService
from ..services.agent_service import AgentService

router = APIRouter()


class ConnectionManager:
    """Manages WebSocket connections"""

    def __init__(self):
        self.active_connections: Dict[str, WebSocket] = {}

    async def connect(self, websocket: WebSocket, session_id: str):
        await websocket.accept()
        self.active_connections[session_id] = websocket

    def disconnect(self, session_id: str):
        if session_id in self.active_connections:
            del self.active_connections[session_id]

    async def send_message(self, session_id: str, message: Dict[str, Any]):
        if session_id in self.active_connections:
            websocket = self.active_connections[session_id]
            await websocket.send_text(json.dumps(message))


manager = ConnectionManager()


@router.websocket("/ws/{session_id}")
async def websocket_endpoint(websocket: WebSocket, session_id: str):
    """WebSocket endpoint for real-time agent communication"""
    await manager.connect(websocket, session_id)

    try:
        while True:
            # Receive message from client
            data = await websocket.receive_text()
            message_data = json.loads(data)

            if message_data.get("type") == "user_message":
                # Get database session
                db = next(get_db())

                try:
                    # Verify session exists
                    session_service = SessionService(db)
                    session = session_service.get_session(session_id)
                    if not session:
                        await websocket.send_text(json.dumps({
                            "type": "error",
                            "message": "Session not found"
                        }))
                        continue

                    # Process message through agent with real-time callbacks
                    agent_service = AgentService(db)

                    async def websocket_callback(data):
                        await manager.send_message(session_id, data)

                    await agent_service.process_user_message(
                        session_id=session_id,
                        user_message=message_data.get("content", ""),
                        websocket_callback=websocket_callback,
                        api_key=message_data.get("api_key")
                    )

                    # Send completion message
                    await manager.send_message(session_id, {
                        "type": "completion",
                        "message": "Agent processing completed"
                    })

                finally:
                    db.close()

    except WebSocketDisconnect:
        manager.disconnect(session_id)
    except Exception as e:
        await websocket.send_text(json.dumps({
            "type": "error",
            "message": str(e)
        }))
        manager.disconnect(session_id)
