version: '3.8'

services:
  # FastAPI Backend
  backend:
    build:
      context: .
      dockerfile: Dockerfile.backend
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=sqlite:///./data/computer_use_backend.db
      - ANTHROPIC_API_KEY=${ANTHROPIC_API_KEY}
      - API_PROVIDER=${API_PROVIDER:-anthropic}
      - HOST=0.0.0.0
      - PORT=8000
      - DEBUG=${DEBUG:-false}
    volumes:
      - ./data:/app/data
      - ./.env:/app/.env
    networks:
      - computer-use-network
    restart: unless-stopped

  # VNC Desktop Environment
  vnc-desktop:
    build:
      context: ..
      dockerfile: backend/Dockerfile.vnc
    ports:
      - "5900:5900"  # VNC server
      - "6080:6080"  # noVNC web interface
      - "8080:8080"  # Combined interface
    environment:
      - ANTHROPIC_API_KEY=${ANTHROPIC_API_KEY}
      - API_PROVIDER=${API_PROVIDER:-anthropic}
      - WIDTH=${WIDTH:-1024}
      - HEIGHT=${HEIGHT:-768}
      - DISPLAY_NUM=1
    volumes:
      - vnc-home:/home/<USER>
    networks:
      - computer-use-network
    restart: unless-stopped
    depends_on:
      - backend

  # PostgreSQL Database (for production)
  postgres:
    image: postgres:15-alpine
    environment:
      - POSTGRES_DB=computer_use_backend
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD:-postgres}
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - computer-use-network
    restart: unless-stopped
    profiles:
      - production

volumes:
  vnc-home:
  postgres_data:

networks:
  computer-use-network:
    driver: bridge
