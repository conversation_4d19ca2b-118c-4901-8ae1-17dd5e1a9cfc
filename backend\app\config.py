"""
Configuration settings for the Claude Computer Use Backend
"""

from pydantic_settings import BaseSettings
from typing import Optional


class Settings(BaseSettings):
    # Database settings
    database_url: str = "sqlite:///./computer_use_backend.db"
    
    # API settings
    anthropic_api_key: Optional[str] = None
    api_provider: str = "anthropic"
    
    # Server settings
    host: str = "0.0.0.0"
    port: int = 8000
    debug: bool = False
    
    # CORS settings
    cors_origins: list[str] = ["http://localhost:3000", "http://localhost:8080"]
    
    # Agent settings
    default_model: str = "claude-sonnet-4-20250514"
    max_tokens: int = 4096
    tool_version: str = "computer_use_20250124"
    
    # VNC settings
    vnc_host: str = "localhost"
    vnc_port: int = 5900
    novnc_port: int = 6080
    
    class Config:
        env_file = ".env"
        case_sensitive = False


settings = Settings()
