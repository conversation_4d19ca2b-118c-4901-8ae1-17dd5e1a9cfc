"""
Message management API endpoints
"""

from typing import List

from fastapi import APIRouter, Depends, HTTPException, status, BackgroundTasks
from sqlalchemy.orm import Session

from ..database import get_db
from ..schemas.message import MessageCreate, MessageResponse
from ..services.message_service import MessageService
from ..services.session_service import SessionService
from ..services.agent_service import AgentService

router = APIRouter(prefix="/sessions", tags=["messages"])


@router.post("/{session_id}/messages", response_model=MessageResponse, status_code=status.HTTP_201_CREATED)
async def send_message(
    session_id: str,
    message_data: MessageCreate,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db)
):
    """Send user query to active session"""
    # Verify session exists
    session_service = SessionService(db)
    session = session_service.get_session(session_id)
    if not session:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Session not found"
        )

    # Create user message
    message_service = MessageService(db)
    user_message = message_service.create_message(
        session_id=session_id,
        message_type="user_query",
        content={"text": message_data.content}
    )

    # Process message through agent in background
    agent_service = AgentService(db)
    background_tasks.add_task(
        agent_service.process_user_message,
        session_id,
        message_data.content
    )

    return user_message


@router.get("/{session_id}/messages", response_model=List[MessageResponse])
def get_messages(
    session_id: str,
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db)
):
    """Retrieve chat history"""
    # Verify session exists
    session_service = SessionService(db)
    session = session_service.get_session(session_id)
    if not session:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Session not found"
        )

    message_service = MessageService(db)
    return message_service.get_messages(session_id, skip=skip, limit=limit)
