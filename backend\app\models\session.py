"""
Session model for storing chat sessions
"""

from sqlalchemy import Column, String, Enum, JSON
from sqlalchemy.orm import relationship
from enum import Enum as PyEnum

from .base import BaseModel


class SessionStatus(PyEnum):
    ACTIVE = "active"
    COMPLETED = "completed"
    FAILED = "failed"


class Session(BaseModel):
    """
    Session model for storing chat sessions
    """
    __tablename__ = "sessions"
    
    title = Column(String(255), nullable=True)
    status = Column(Enum(SessionStatus), default=SessionStatus.ACTIVE, nullable=False)
    metadata = Column(JSON, nullable=True)
    
    # Relationship to messages
    messages = relationship("Message", back_populates="session", cascade="all, delete-orphan")
