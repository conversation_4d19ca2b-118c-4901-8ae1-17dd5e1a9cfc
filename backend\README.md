# Claude Computer Use Backend

**Author: <PERSON><PERSON>**

A production-ready FastAPI backend for Claude Computer Use with session management, real-time streaming, and database persistence. This system transforms the experimental Streamlit demo into a scalable backend infrastructure similar to OpenAI's Operator.

**Last Updated: 2025-07-12T06:14:51.456Z**

## Architecture Overview

### System Components

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │   FastAPI       │    │   VNC Desktop   │
│   (HTML/JS)     │◄──►│   Backend       │◄──►│   Environment   │
│                 │    │                 │    │                 │
│ - Session Mgmt  │    │ - REST API      │    │ - Ubuntu Desktop│
│ - Real-time UI  │    │ - WebSocket     │    │ - Firefox       │
│ - VNC Viewer    │    │ - Agent Service │    │ - Computer Tools│
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │
                       ┌─────────────────┐
                       │   Database      │
                       │   (SQLite/PG)   │
                       │                 │
                       │ - Sessions      │
                       │ - Messages      │
                       │ - Chat History  │
                       └─────────────────┘
```

### Key Features

-   **Session-based Chat Management**: Each user interaction is treated as a persistent conversation
-   **Real-time Streaming**: WebSocket-based streaming of agent reasoning, function calls, and results
-   **Database Persistence**: Complete conversation history and session metadata storage
-   **Computer Use Integration**: Full preservation of Anthropic's computer use agent capabilities
-   **VNC Integration**: Seamless desktop environment access for visual tasks
-   **Production Ready**: Proper error handling, logging, and scalability considerations

## Prerequisites

-   Docker and Docker Compose
-   Anthropic API Key
-   Python 3.11+ (for local development)

## Quick Start

### 1. Clone and Setup

```bash
git clone <repository-url>
cd backend
cp .env.example .env
```

### 2. Configure Environment

Edit `.env` file:

```bash
ANTHROPIC_API_KEY=your_anthropic_api_key_here
API_PROVIDER=anthropic
DATABASE_URL=sqlite:///./data/computer_use_backend.db
```

### 3. Run with Docker Compose

```bash
# Development (SQLite)
docker-compose up --build

# Production (PostgreSQL)
POSTGRES_PASSWORD=your_secure_password docker-compose --profile production up --build
```

### 4. Access the Application

-   **Frontend Demo**: http://localhost:8000/static/index.html
-   **API Documentation**: http://localhost:8000/docs
-   **VNC Desktop**: http://localhost:6080/vnc.html
-   **Combined Interface**: http://localhost:8080

## API Documentation

### Session Management

#### Create Session

```http
POST /api/sessions
Content-Type: application/json

{
  "title": "Weather Search Session",
  "metadata": {"purpose": "weather_queries"}
}
```

#### List Sessions

```http
GET /api/sessions?skip=0&limit=100
```

#### Get Session Details

```http
GET /api/sessions/{session_id}
```

#### Delete Session

```http
DELETE /api/sessions/{session_id}
```

### Message Management

#### Send Message

```http
POST /api/sessions/{session_id}/messages
Content-Type: application/json

{
  "content": "Search the weather in Dubai"
}
```

#### Get Chat History

```http
GET /api/sessions/{session_id}/messages?skip=0&limit=100
```

### Real-time Streaming

#### WebSocket Connection

```javascript
const ws = new WebSocket("ws://localhost:8000/ws/{session_id}");

// Send message
ws.send(
    JSON.stringify({
        type: "user_message",
        content: "Search the weather in San Francisco",
        api_key: "your_api_key",
    })
);

// Receive real-time updates
ws.onmessage = function (event) {
    const data = JSON.parse(event.data);
    // Handle: agent_response, tool_result, completion, error
};
```

## Local Development Setup

### 1. Install Dependencies

```bash
cd backend
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
pip install -r requirements.txt
```

### 2. Run Backend

```bash
python run.py
```

### 3. Run VNC Environment (separate terminal)

```bash
cd ..
docker run \
    -e ANTHROPIC_API_KEY=$ANTHROPIC_API_KEY \
    -p 5900:5900 \
    -p 6080:6080 \
    -p 8080:8080 \
    -it ghcr.io/anthropics/anthropic-quickstarts:computer-use-demo-latest
```

## Project Structure

```
backend/
├── app/
│   ├── main.py              # FastAPI application entry point
│   ├── config.py            # Configuration management
│   ├── database.py          # Database setup and session management
│   ├── models/              # SQLAlchemy models
│   │   ├── session.py       # Session model
│   │   └── message.py       # Message model
│   ├── schemas/             # Pydantic schemas for API
│   │   ├── session.py       # Session request/response schemas
│   │   └── message.py       # Message request/response schemas
│   ├── routers/             # API route handlers
│   │   ├── sessions.py      # Session management endpoints
│   │   ├── messages.py      # Message endpoints
│   │   └── websocket.py     # WebSocket for real-time streaming
│   ├── services/            # Business logic layer
│   │   ├── session_service.py
│   │   ├── message_service.py
│   │   └── agent_service.py # Computer use agent integration
│   └── agent/               # Computer use agent (from original)
│       ├── loop.py          # Agent sampling loop
│       └── tools/           # All computer use tools
├── frontend/                # Simple HTML/JS demo interface
│   ├── index.html
│   ├── style.css
│   └── script.js
├── docker-compose.yml       # Multi-service Docker setup
├── Dockerfile.backend       # FastAPI backend container
├── Dockerfile.vnc           # VNC desktop container
├── requirements.txt         # Python dependencies
└── .env.example            # Environment variables template
```

## Database Schema

### Sessions Table

```sql
CREATE TABLE sessions (
    id VARCHAR(36) PRIMARY KEY,
    title VARCHAR(255),
    status VARCHAR(20) DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    metadata JSON
);
```

### Messages Table

```sql
CREATE TABLE messages (
    id VARCHAR(36) PRIMARY KEY,
    session_id VARCHAR(36) REFERENCES sessions(id) ON DELETE CASCADE,
    type VARCHAR(50) NOT NULL,
    content JSON NOT NULL,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    metadata JSON
);
```

## Key Features Implementation

### Session Management

-   **Isolated Sessions**: Each conversation is completely isolated with its own context
-   **Persistent Storage**: All sessions and messages are stored in the database
-   **Metadata Support**: Custom metadata can be attached to sessions for organization
-   **Status Tracking**: Sessions can be marked as active, completed, or failed

### Real-time Streaming

-   **WebSocket Communication**: Bidirectional real-time communication per session
-   **Event Types**: Supports agent_response, tool_result, completion, and error events
-   **Connection Management**: Automatic connection handling and reconnection
-   **Message Ordering**: Maintains proper message sequence and session state

### Computer Use Integration

-   **Tool Preservation**: All original computer use tools are preserved and functional
-   **Agent Loop**: The core sampling loop is adapted for backend use while maintaining functionality
-   **Callback System**: Real-time callbacks for streaming agent actions and tool results
-   **Error Handling**: Comprehensive error handling for agent failures and API issues

## Tech Stack

-   **Backend Framework**: FastAPI 0.104.1
-   **Database**: SQLAlchemy with SQLite (development) / PostgreSQL (production)
-   **Real-time Communication**: WebSockets
-   **Agent Integration**: Anthropic Computer Use Agent
-   **Desktop Environment**: Ubuntu 22.04 with VNC/noVNC
-   **Containerization**: Docker and Docker Compose

## Environment Variables

| Variable            | Description                             | Default                               |
| ------------------- | --------------------------------------- | ------------------------------------- |
| `DATABASE_URL`      | Database connection string              | `sqlite:///./computer_use_backend.db` |
| `ANTHROPIC_API_KEY` | Anthropic API key                       | Required                              |
| `API_PROVIDER`      | API provider (anthropic/bedrock/vertex) | `anthropic`                           |
| `HOST`              | Server host                             | `0.0.0.0`                             |
| `PORT`              | Server port                             | `8000`                                |
| `DEBUG`             | Debug mode                              | `false`                               |
| `DEFAULT_MODEL`     | Default Claude model                    | `claude-sonnet-4-20250514`            |
| `MAX_TOKENS`        | Maximum tokens per request              | `4096`                                |
| `VNC_PORT`          | VNC server port                         | `5900`                                |
| `NOVNC_PORT`        | noVNC web interface port                | `6080`                                |

## Testing the System

### Test Case 1: Dubai Weather Search

1. Create a new session via the frontend
2. Send message: "Search the weather in Dubai"
3. Observe real-time streaming of agent actions
4. Verify Firefox opens and performs Google search
5. Check that weather results are summarized

### Test Case 2: San Francisco Weather Search

1. Create another session
2. Send message: "Search the weather in San Francisco"
3. Verify independent session management
4. Confirm both sessions persist in database
5. Test switching between sessions

## Troubleshooting

### Common Issues

**Backend won't start**

-   Check that port 8000 is available
-   Verify ANTHROPIC_API_KEY is set correctly
-   Check database permissions

**VNC connection fails**

-   Ensure ports 5900 and 6080 are available
-   Check Docker container logs
-   Verify VNC service is running

**WebSocket connection drops**

-   Check network connectivity
-   Verify session ID is valid
-   Monitor browser console for errors

**Agent responses are slow**

-   Check Anthropic API rate limits
-   Monitor system resources
-   Verify network latency to Anthropic API

## License

This project extends the original Anthropic Computer Use Demo under the same license terms.

## Author

**Chirag Singhal** (`chirag127`)

-   GitHub: https://github.com/chirag127
-   Email: <EMAIL>
