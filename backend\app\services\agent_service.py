"""
Agent service for integrating computer use functionality
"""

import asyncio
from typing import Callable, List, Optional, Dict, Any

from sqlalchemy.orm import Session

from ..agent.loop import sampling_loop, APIProvider
from ..agent.tools import ToolVersion
from ..config import settings
from .message_service import MessageService


class AgentService:
    """
    Service class for agent operations
    """

    def __init__(self, db: Session):
        self.db = db
        self.message_service = MessageService(db)

    async def process_user_message(
        self,
        session_id: str,
        user_message: str,
        websocket_callback: Optional[Callable] = None,
        api_key: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """
        Process a user message through the agent
        """
        # Store user message
        user_msg = self.message_service.create_message(
            session_id=session_id,
            message_type="user_query",
            content={"text": user_message}
        )

        # Get conversation history
        messages = self._build_conversation_history(session_id)

        # Add current user message
        messages.append({
            "role": "user",
            "content": [{"type": "text", "text": user_message}]
        })

        # Prepare callbacks
        def output_callback(content_block):
            if websocket_callback:
                asyncio.create_task(websocket_callback({
                    "type": "agent_response",
                    "content": content_block
                }))

            # Store agent response
            self.message_service.create_message(
                session_id=session_id,
                message_type="agent_response",
                content=content_block
            )

        def tool_output_callback(tool_result, tool_id):
            if websocket_callback:
                asyncio.create_task(websocket_callback({
                    "type": "tool_result",
                    "tool_id": tool_id,
                    "result": {
                        "output": tool_result.output,
                        "error": tool_result.error,
                        "base64_image": tool_result.base64_image
                    }
                }))

            # Store tool result
            self.message_service.create_message(
                session_id=session_id,
                message_type="tool_result",
                content={
                    "tool_id": tool_id,
                    "output": tool_result.output,
                    "error": tool_result.error,
                    "base64_image": tool_result.base64_image
                }
            )

        def api_response_callback(request, response, error):
            # Log API responses if needed
            pass

        # Run the agent sampling loop
        try:
            final_messages = await sampling_loop(
                model=settings.default_model,
                provider=APIProvider.ANTHROPIC,
                system_prompt_suffix="",
                messages=messages,
                output_callback=output_callback,
                tool_output_callback=tool_output_callback,
                api_response_callback=api_response_callback,
                api_key=api_key or settings.anthropic_api_key,
                max_tokens=settings.max_tokens,
                tool_version=settings.tool_version
            )

            return final_messages

        except Exception as e:
            # Store error message
            self.message_service.create_message(
                session_id=session_id,
                message_type="agent_response",
                content={"error": str(e)}
            )
            raise e

    def _build_conversation_history(self, session_id: str) -> List[Dict[str, Any]]:
        """
        Build conversation history from stored messages
        """
        messages = self.message_service.get_messages(session_id)
        conversation = []

        for msg in messages:
            if msg.type == "user_query":
                conversation.append({
                    "role": "user",
                    "content": [{"type": "text", "text": msg.content.get("text", "")}]
                })
            elif msg.type == "agent_response" and not msg.content.get("error"):
                conversation.append({
                    "role": "assistant",
                    "content": [msg.content]
                })

        return conversation
