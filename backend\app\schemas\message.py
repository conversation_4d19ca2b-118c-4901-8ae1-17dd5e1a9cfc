"""
Pydantic schemas for message API
"""

from pydantic import BaseModel
from typing import Optional, Dict, Any
from datetime import datetime
from uuid import UUID

from ..models.message import MessageType


class MessageBase(BaseModel):
    type: str
    content: Dict[str, Any]
    message_metadata: Optional[Dict[str, Any]] = None


class MessageCreate(BaseModel):
    content: str  # User message content


class MessageResponse(MessageBase):
    id: str
    session_id: str
    timestamp: datetime

    class Config:
        from_attributes = True
