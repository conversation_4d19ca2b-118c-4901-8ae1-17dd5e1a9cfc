<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Claude Computer Use Backend Demo</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="container">
        <header>
            <h1><PERSON> Computer Use Backend Demo</h1>
            <p>Production-ready backend with session management and real-time streaming</p>
        </header>

        <div class="main-content">
            <!-- Session Management Panel -->
            <div class="session-panel">
                <h2>Session Management</h2>
                <div class="session-controls">
                    <button id="create-session" class="btn btn-primary">Create New Session</button>
                    <button id="refresh-sessions" class="btn btn-secondary">Refresh Sessions</button>
                </div>
                <div class="sessions-list" id="sessions-list">
                    <!-- Sessions will be populated here -->
                </div>
            </div>

            <!-- Chat Interface -->
            <div class="chat-panel">
                <h2>Chat Interface</h2>
                <div class="current-session" id="current-session">
                    <span>No session selected</span>
                </div>
                <div class="chat-messages" id="chat-messages">
                    <!-- Messages will appear here -->
                </div>
                <div class="chat-input-container">
                    <input type="text" id="chat-input" placeholder="Type your message..." disabled>
                    <button id="send-message" class="btn btn-primary" disabled>Send</button>
                </div>
                <div class="connection-status" id="connection-status">
                    Disconnected
                </div>
            </div>

            <!-- VNC Viewer -->
            <div class="vnc-panel">
                <h2>VNC Desktop View</h2>
                <div class="vnc-container">
                    <iframe id="vnc-frame" src="http://localhost:6080/vnc.html" width="100%" height="400px">
                        VNC viewer not available
                    </iframe>
                </div>
            </div>
        </div>

        <!-- API Key Modal -->
        <div id="api-key-modal" class="modal">
            <div class="modal-content">
                <h3>Enter Anthropic API Key</h3>
                <input type="password" id="api-key-input" placeholder="Your Anthropic API Key">
                <div class="modal-buttons">
                    <button id="save-api-key" class="btn btn-primary">Save</button>
                    <button id="cancel-api-key" class="btn btn-secondary">Cancel</button>
                </div>
            </div>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
