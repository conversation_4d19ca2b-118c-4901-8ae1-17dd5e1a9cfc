"""
Session service for business logic
"""

from typing import List, Optional

from sqlalchemy.orm import Session

from ..models.session import Session as SessionModel, SessionStatus
from ..schemas.session import SessionCreate, SessionUpdate


class SessionService:
    """
    Service class for session operations
    """

    def __init__(self, db: Session):
        self.db = db

    def create_session(self, session_data: SessionCreate) -> SessionModel:
        """Create a new session"""
        db_session = SessionModel(
            title=session_data.title,
            session_metadata=session_data.session_metadata
        )
        self.db.add(db_session)
        self.db.commit()
        self.db.refresh(db_session)
        return db_session

    def get_session(self, session_id: str) -> Optional[SessionModel]:
        """Get a session by ID"""
        return self.db.query(SessionModel).filter(SessionModel.id == session_id).first()

    def get_sessions(self, skip: int = 0, limit: int = 100) -> List[SessionModel]:
        """Get all sessions with pagination"""
        return self.db.query(SessionModel).offset(skip).limit(limit).all()

    def update_session(self, session_id: str, session_data: SessionUpdate) -> Optional[SessionModel]:
        """Update a session"""
        db_session = self.get_session(session_id)
        if not db_session:
            return None

        if session_data.title is not None:
            db_session.title = session_data.title
        if session_data.status is not None:
            db_session.status = session_data.status
        if session_data.session_metadata is not None:
            db_session.session_metadata = session_data.session_metadata

        self.db.commit()
        self.db.refresh(db_session)
        return db_session

    def delete_session(self, session_id: str) -> bool:
        """Delete a session"""
        db_session = self.get_session(session_id)
        if not db_session:
            return False

        self.db.delete(db_session)
        self.db.commit()
        return True
