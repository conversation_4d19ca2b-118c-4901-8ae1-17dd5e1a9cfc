// Global state
let currentSession = null;
let websocket = null;
let apiKey = localStorage.getItem('anthropic_api_key') || '';

// API base URL
const API_BASE = 'http://localhost:8000/api';
const WS_BASE = 'ws://localhost:8000';

// DOM elements
const elements = {
    createSession: document.getElementById('create-session'),
    refreshSessions: document.getElementById('refresh-sessions'),
    sessionsList: document.getElementById('sessions-list'),
    currentSessionDisplay: document.getElementById('current-session'),
    chatMessages: document.getElementById('chat-messages'),
    chatInput: document.getElementById('chat-input'),
    sendMessage: document.getElementById('send-message'),
    connectionStatus: document.getElementById('connection-status'),
    apiKeyModal: document.getElementById('api-key-modal'),
    apiKeyInput: document.getElementById('api-key-input'),
    saveApiKey: document.getElementById('save-api-key'),
    cancelApiKey: document.getElementById('cancel-api-key')
};

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    setupEventListeners();
    loadSessions();
    
    // Show API key modal if not set
    if (!apiKey) {
        showApiKeyModal();
    }
});

// Event listeners
function setupEventListeners() {
    elements.createSession.addEventListener('click', createSession);
    elements.refreshSessions.addEventListener('click', loadSessions);
    elements.sendMessage.addEventListener('click', sendMessage);
    elements.chatInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            sendMessage();
        }
    });
    
    // API key modal
    elements.saveApiKey.addEventListener('click', saveApiKey);
    elements.cancelApiKey.addEventListener('click', hideApiKeyModal);
}

// API functions
async function apiRequest(endpoint, options = {}) {
    try {
        const response = await fetch(`${API_BASE}${endpoint}`, {
            headers: {
                'Content-Type': 'application/json',
                ...options.headers
            },
            ...options
        });
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        return await response.json();
    } catch (error) {
        console.error('API request failed:', error);
        addMessage('error', `API Error: ${error.message}`);
        throw error;
    }
}

// Session management
async function createSession() {
    try {
        const session = await apiRequest('/sessions', {
            method: 'POST',
            body: JSON.stringify({
                title: `Session ${new Date().toLocaleTimeString()}`,
                metadata: { created_by: 'demo_frontend' }
            })
        });
        
        addMessage('system', `Created new session: ${session.id}`);
        loadSessions();
        selectSession(session.id);
    } catch (error) {
        console.error('Failed to create session:', error);
    }
}

async function loadSessions() {
    try {
        const sessions = await apiRequest('/sessions');
        displaySessions(sessions);
    } catch (error) {
        console.error('Failed to load sessions:', error);
    }
}

function displaySessions(sessions) {
    elements.sessionsList.innerHTML = '';
    
    sessions.forEach(session => {
        const sessionElement = document.createElement('div');
        sessionElement.className = 'session-item';
        sessionElement.dataset.sessionId = session.id;
        
        sessionElement.innerHTML = `
            <div class="session-title">${session.title || 'Untitled Session'}</div>
            <div class="session-meta">
                ${session.status} • ${new Date(session.created_at).toLocaleString()}
            </div>
        `;
        
        sessionElement.addEventListener('click', () => selectSession(session.id));
        elements.sessionsList.appendChild(sessionElement);
    });
}

function selectSession(sessionId) {
    // Update UI
    document.querySelectorAll('.session-item').forEach(item => {
        item.classList.remove('active');
    });
    
    const selectedItem = document.querySelector(`[data-session-id="${sessionId}"]`);
    if (selectedItem) {
        selectedItem.classList.add('active');
    }
    
    currentSession = sessionId;
    elements.currentSessionDisplay.textContent = `Session: ${sessionId}`;
    elements.chatInput.disabled = false;
    elements.sendMessage.disabled = false;
    
    // Clear chat and load messages
    elements.chatMessages.innerHTML = '';
    loadMessages(sessionId);
    
    // Connect WebSocket
    connectWebSocket(sessionId);
}

async function loadMessages(sessionId) {
    try {
        const messages = await apiRequest(`/sessions/${sessionId}/messages`);
        messages.forEach(message => {
            displayMessage(message);
        });
    } catch (error) {
        console.error('Failed to load messages:', error);
    }
}

// WebSocket connection
function connectWebSocket(sessionId) {
    if (websocket) {
        websocket.close();
    }
    
    websocket = new WebSocket(`${WS_BASE}/ws/${sessionId}`);
    
    websocket.onopen = function() {
        updateConnectionStatus('connected');
        addMessage('system', 'Connected to real-time stream');
    };
    
    websocket.onmessage = function(event) {
        const data = JSON.parse(event.data);
        handleWebSocketMessage(data);
    };
    
    websocket.onclose = function() {
        updateConnectionStatus('disconnected');
        addMessage('system', 'Disconnected from real-time stream');
    };
    
    websocket.onerror = function(error) {
        console.error('WebSocket error:', error);
        addMessage('error', 'WebSocket connection error');
    };
}

function handleWebSocketMessage(data) {
    switch (data.type) {
        case 'agent_response':
            if (data.content.type === 'text') {
                addMessage('agent', data.content.text);
            } else if (data.content.type === 'thinking') {
                addMessage('thinking', data.content.thinking);
            } else if (data.content.type === 'tool_use') {
                addMessage('tool', `Tool: ${data.content.name}\nInput: ${JSON.stringify(data.content.input, null, 2)}`);
            }
            break;
        case 'tool_result':
            let toolMessage = `Tool Result (${data.tool_id}):\n`;
            if (data.result.output) {
                toolMessage += data.result.output;
            }
            if (data.result.error) {
                toolMessage += `Error: ${data.result.error}`;
            }
            addMessage('tool', toolMessage);
            break;
        case 'completion':
            addMessage('system', data.message);
            break;
        case 'error':
            addMessage('error', data.message);
            break;
    }
}

// Message handling
function sendMessage() {
    const message = elements.chatInput.value.trim();
    if (!message || !currentSession) return;
    
    addMessage('user', message);
    elements.chatInput.value = '';
    
    // Send via WebSocket
    if (websocket && websocket.readyState === WebSocket.OPEN) {
        websocket.send(JSON.stringify({
            type: 'user_message',
            content: message,
            api_key: apiKey
        }));
    }
}

function addMessage(type, content) {
    const messageElement = document.createElement('div');
    messageElement.className = `message ${type}`;
    
    messageElement.innerHTML = `
        <div class="message-content">${content}</div>
        <div class="message-meta">${new Date().toLocaleTimeString()}</div>
    `;
    
    elements.chatMessages.appendChild(messageElement);
    elements.chatMessages.scrollTop = elements.chatMessages.scrollHeight;
}

function displayMessage(message) {
    let type = 'agent';
    let content = '';
    
    if (message.type === 'user_query') {
        type = 'user';
        content = message.content.text || JSON.stringify(message.content);
    } else if (message.type === 'agent_response') {
        type = 'agent';
        content = message.content.text || JSON.stringify(message.content);
    } else if (message.type === 'tool_result') {
        type = 'tool';
        content = message.content.output || message.content.error || JSON.stringify(message.content);
    }
    
    addMessage(type, content);
}

// Utility functions
function updateConnectionStatus(status) {
    elements.connectionStatus.textContent = status === 'connected' ? 'Connected' : 'Disconnected';
    elements.connectionStatus.className = `connection-status ${status}`;
}

// API Key modal
function showApiKeyModal() {
    elements.apiKeyModal.style.display = 'block';
    elements.apiKeyInput.value = apiKey;
}

function hideApiKeyModal() {
    elements.apiKeyModal.style.display = 'none';
}

function saveApiKey() {
    apiKey = elements.apiKeyInput.value.trim();
    if (apiKey) {
        localStorage.setItem('anthropic_api_key', apiKey);
        hideApiKeyModal();
        addMessage('system', 'API key saved');
    } else {
        alert('Please enter a valid API key');
    }
}
