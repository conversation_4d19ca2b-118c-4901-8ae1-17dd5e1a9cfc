/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background-color: #f5f5f5;
    color: #333;
    line-height: 1.6;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
}

/* Header */
header {
    text-align: center;
    margin-bottom: 30px;
    padding: 20px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

header h1 {
    color: #2c3e50;
    margin-bottom: 10px;
}

header p {
    color: #7f8c8d;
}

/* Main content layout */
.main-content {
    display: grid;
    grid-template-columns: 300px 1fr 400px;
    gap: 20px;
    height: 80vh;
}

/* Panel styles */
.session-panel, .chat-panel, .vnc-panel {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    display: flex;
    flex-direction: column;
}

.session-panel h2, .chat-panel h2, .vnc-panel h2 {
    margin-bottom: 15px;
    color: #2c3e50;
    border-bottom: 2px solid #3498db;
    padding-bottom: 5px;
}

/* Session controls */
.session-controls {
    display: flex;
    gap: 10px;
    margin-bottom: 15px;
}

.sessions-list {
    flex: 1;
    overflow-y: auto;
}

.session-item {
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    margin-bottom: 8px;
    cursor: pointer;
    transition: background-color 0.2s;
}

.session-item:hover {
    background-color: #f8f9fa;
}

.session-item.active {
    background-color: #3498db;
    color: white;
}

.session-item .session-title {
    font-weight: bold;
    margin-bottom: 4px;
}

.session-item .session-meta {
    font-size: 0.8em;
    color: #666;
}

.session-item.active .session-meta {
    color: #ecf0f1;
}

/* Chat interface */
.current-session {
    background-color: #ecf0f1;
    padding: 10px;
    border-radius: 4px;
    margin-bottom: 15px;
    font-weight: bold;
}

.chat-messages {
    flex: 1;
    overflow-y: auto;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 15px;
    margin-bottom: 15px;
    background-color: #fafafa;
}

.message {
    margin-bottom: 15px;
    padding: 10px;
    border-radius: 8px;
    max-width: 80%;
}

.message.user {
    background-color: #3498db;
    color: white;
    margin-left: auto;
}

.message.agent {
    background-color: #2ecc71;
    color: white;
}

.message.tool {
    background-color: #f39c12;
    color: white;
}

.message.error {
    background-color: #e74c3c;
    color: white;
}

.message.thinking {
    background-color: #9b59b6;
    color: white;
    font-style: italic;
}

.message-content {
    margin-bottom: 5px;
}

.message-meta {
    font-size: 0.8em;
    opacity: 0.8;
}

.chat-input-container {
    display: flex;
    gap: 10px;
}

#chat-input {
    flex: 1;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.connection-status {
    margin-top: 10px;
    padding: 5px 10px;
    border-radius: 4px;
    text-align: center;
    font-weight: bold;
}

.connection-status.connected {
    background-color: #2ecc71;
    color: white;
}

.connection-status.disconnected {
    background-color: #e74c3c;
    color: white;
}

/* VNC panel */
.vnc-container {
    flex: 1;
    border: 1px solid #ddd;
    border-radius: 4px;
    overflow: hidden;
}

#vnc-frame {
    border: none;
}

/* Buttons */
.btn {
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.2s;
}

.btn-primary {
    background-color: #3498db;
    color: white;
}

.btn-primary:hover {
    background-color: #2980b9;
}

.btn-secondary {
    background-color: #95a5a6;
    color: white;
}

.btn-secondary:hover {
    background-color: #7f8c8d;
}

.btn:disabled {
    background-color: #bdc3c7;
    cursor: not-allowed;
}

/* Modal */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
}

.modal-content {
    background-color: white;
    margin: 15% auto;
    padding: 20px;
    border-radius: 8px;
    width: 400px;
    text-align: center;
}

.modal-content h3 {
    margin-bottom: 15px;
    color: #2c3e50;
}

#api-key-input {
    width: 100%;
    padding: 10px;
    margin-bottom: 15px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.modal-buttons {
    display: flex;
    gap: 10px;
    justify-content: center;
}

/* Responsive design */
@media (max-width: 1200px) {
    .main-content {
        grid-template-columns: 1fr;
        grid-template-rows: auto auto auto;
        height: auto;
    }
    
    .session-panel, .chat-panel, .vnc-panel {
        height: 400px;
    }
}
