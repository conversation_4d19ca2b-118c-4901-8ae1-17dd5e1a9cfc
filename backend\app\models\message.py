"""
Message model for storing chat messages
"""

from sqlalchemy import <PERSON>um<PERSON>, <PERSON>, JSO<PERSON>, Foreign<PERSON><PERSON>, DateTime
from sqlalchemy.orm import relationship
from enum import Enum as PyEnum
from datetime import datetime

from .base import BaseModel


class MessageType(PyEnum):
    USER_QUERY = "user_query"
    AGENT_RESPONSE = "agent_response"
    FUNCTION_CALL = "function_call"
    TOOL_RESULT = "tool_result"


class Message(BaseModel):
    """
    Message model for storing chat messages
    """
    __tablename__ = "messages"

    session_id = Column(String(36), ForeignKey("sessions.id", ondelete="CASCADE"), nullable=False)
    type = Column(String(50), nullable=False)
    content = Column(JSON, nullable=False)
    timestamp = Column(DateTime, default=datetime.utcnow, nullable=False)
    message_metadata = Column(JSON, nullable=True)

    # Relationship to session
    session = relationship("Session", back_populates="messages")
